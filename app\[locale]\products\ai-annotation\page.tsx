import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { getProductDetails } from '@/lib/products-data'
import { ProductDetailClient } from './components/ProductDetailClient'
import { notFound } from 'next/navigation'

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'

interface Props {
  params: { locale: string }
}

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale })
  const productDetails = getProductDetails(t)
  const product = productDetails['ai-annotation']

  if (!product) {
    return {
      title: 'Product Not Found',
      description: 'The requested product could not be found.'
    }
  }

  const isEnglish = locale === 'en'
  const title = isEnglish 
    ? `${product.name} - zerodots` 
    : `${product.name} - 零点科技`
  



  const description = product.description
  return {
    title,
    description,
    keywords: isEnglish 
      ? ['AI annotation', 'data labeling', 'machine learning', 'computer vision', 'NLP', 'artificial intelligence']
      : ['AI智能标注', '数据标注', '机器学习', '计算机视觉', '自然语言处理', '人工智能'],
    openGraph: {
      title,
      description,
      url: `${baseUrl}/${locale}/products/ai-annotation`,
      siteName: isEnglish ? 'zerodots' : '零点科技',
      images: [
        {
          url: `${baseUrl}/og-ai-annotation.jpg`,
          width: 1200,
          height: 630,
          alt: product.name,
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`${baseUrl}/og-ai-annotation.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/products/ai-annotation`,
      languages: {
        'zh-CN': `${baseUrl}/zh/products/ai-annotation`,
        'en-US': `${baseUrl}/en/products/ai-annotation`,
      },
    },
  }
}

export default async function AIAnnotationPage({ params: { locale } }: Props) {
  const t = await getTranslations({ locale })
  const productDetails = getProductDetails(t)
  const product = productDetails['ai-annotation']

  if (!product) {
    notFound()
  }

  return <ProductDetailClient product={product} productSlug="ai-annotation" />
}
