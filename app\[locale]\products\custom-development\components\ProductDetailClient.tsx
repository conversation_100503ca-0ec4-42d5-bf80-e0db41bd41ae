"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useTranslations, useRawTranslations, useLocale } from '@/hooks/useTranslations'
import {
  Code,
  Globe,
  Smartphone,
  Building,
  Settings,
  CheckCircle,
  Star,
  TrendingUp,
  BarChart,
  Shield,
  Zap,
  Users,
  ArrowRight,
  Sparkles,
  Target,
  Award,
  Clock,
  HeartHandshake,
  Cloud,
  Coins,
  Layers,
  GitBranch
} from "lucide-react"

interface ProductDetail {
  name: string
  description: string
  features: string[]
  techSpecs: {
    deployment: string
    security: string
    availability: string
    support: string
  }
  featureList?: Array<{
    title: string
    description: string
    features: Array<{
      name: string
      description: string
      icon: string
    }>
  }>
  demoVideo?: {
    url: string
    thumbnail: string
  }
  benefits?: Array<{
    title: string
    description: string
  }>
}

interface Props {
  product: ProductDetail
  productSlug: string
}

const iconMap = {
  Code,
  Globe,
  Smartphone,
  Building,
  Settings,
  CheckCircle,
  Star,
  TrendingUp,
  BarChart,
  Shield,
  Zap,
  Users,
  Target,
  Award,
  Clock,
  HeartHandshake,
  Cloud,
  Coins,
  Layers,
  GitBranch
}

export function ProductDetailClient({ product, productSlug }: Props) {
  const t = useTranslations('productDetails.customDevelopment')
  const tCustomDev = useTranslations('customDevelopment')
  const rawCustomDev = useRawTranslations('customDevelopment')
  const tCommon = useTranslations('common')
  const locale = useLocale()
  
  // Define content based on locale
  const content = {
    zh: {
      services: [
        {
          icon: "Globe",
          title: "Web应用开发",
          description: "基于最新Web技术栈开发高性能、响应式的Web应用系统",
          color: "from-blue-500 to-blue-600",
          tech: "React, Vue.js, Angular"
        },
        {
          icon: "Smartphone", 
          title: "移动应用开发",
          description: "开发iOS、Android原生应用或跨平台应用，提供优质的移动用户体验",
          color: "from-green-500 to-green-600",
          tech: "Swift, Kotlin, Java"
        },
        {
          icon: "Code",
          title: "区块链开发",
          description: "提供完整的区块链应用开发服务，包括智能合约、DApp、NFT平台等",
          color: "from-purple-500 to-purple-600",
          tech: "Ethereum, Polygon, BSC"
        },
        {
          icon: "Building",
          title: "企业软件开发",
          description: "开发ERP、CRM、OA等企业管理系统，提升企业运营效率",
          color: "from-indigo-500 to-indigo-600",
          tech: "微服务架构"
        },
        {
          icon: "Settings",
          title: "API开发与集成",
          description: "开发RESTful API、GraphQL接口，实现系统间的数据交互和集成",
          color: "from-orange-500 to-orange-600",
          tech: "REST, GraphQL, gRPC"
        },
        {
          icon: "Cloud",
          title: "云端迁移服务",
          description: "帮助企业将传统应用迁移到云平台，实现数字化转型",
          color: "from-cyan-500 to-cyan-600",
          tech: "AWS, Azure, 阿里云"
        }
      ],
      blockchain: {
        title: "区块链开发",
        description: "提供完整的区块链应用开发服务，包括智能合约、DApp、NFT平台等",
        specialties: [
          {
            icon: 'Code',
            title: '智能合约开发',
            description: 'ERC-20, ERC-721, ERC-1155等标准智能合约开发'
          },
          {
            icon: 'Globe',
            title: 'DApp应用开发',
            description: '去中心化应用前后端开发，Web3集成'
          },
          {
            icon: 'Layers',
            title: 'NFT平台开发',
            description: 'NFT铸造、交易、拍卖平台开发'
          },
          {
            icon: 'TrendingUp',
            title: 'DeFi协议开发',
            description: '去中心化金融协议和产品开发'
          },
          {
            icon: 'Coins',
            title: '代币经济设计',
            description: '代币经济模型设计和智能合约实现'
          },
          {
            icon: 'Shield',
            title: '安全审计',
            description: '智能合约安全审计和漏洞检测'
          }
        ]
      },
      techSpecs: [
        { key: 'methodology', value: '敏捷开发' },
        { key: 'security', value: '企业级安全' },
        { key: 'scalability', value: '高扩展性' },
        { key: 'support', value: '24/7技术支持' },
        { key: 'deployment', value: '多环境部署' },
        { key: 'integration', value: '第三方集成' },
        { key: 'testing', value: '全面测试' },
        { key: 'maintenance', value: '持续维护' }
      ]
    },
    en: {
      services: [
        {
          icon: "Globe",
          title: "Web Application Development",
          description: "Develop high-performance, responsive web application systems based on the latest web technology stack",
          color: "from-blue-500 to-blue-600",
          tech: "React, Vue.js, Angular"
        },
        {
          icon: "Smartphone", 
          title: "Mobile Application Development",
          description: "Develop iOS, Android native applications or cross-platform apps, providing excellent mobile user experience",
          color: "from-green-500 to-green-600",
          tech: "Swift, Kotlin, Java"
        },
        {
          icon: "Code",
          title: "Blockchain Development",
          description: "Provide complete blockchain application development services including smart contracts, DApps, NFT platforms, etc.",
          color: "from-purple-500 to-purple-600",
          tech: "Ethereum, Polygon, BSC"
        },
        {
          icon: "Building",
          title: "Enterprise Software Development",
          description: "Develop ERP, CRM, OA and other enterprise management systems to improve operational efficiency",
          color: "from-indigo-500 to-indigo-600",
          tech: "Microservices Architecture"
        },
        {
          icon: "Settings",
          title: "API Development & Integration",
          description: "Develop RESTful APIs, GraphQL interfaces for data interaction and integration between systems",
          color: "from-orange-500 to-orange-600",
          tech: "REST, GraphQL, gRPC"
        },
        {
          icon: "Cloud",
          title: "Cloud Migration Services",
          description: "Help enterprises migrate traditional applications to cloud platforms for digital transformation",
          color: "from-cyan-500 to-cyan-600",
          tech: "AWS, Azure, Alibaba Cloud"
        }
      ],
      blockchain: {
        title: "Blockchain Development",
        description: "Provide complete blockchain application development services including smart contracts, DApps, NFT platforms, etc.",
        specialties: [
          {
            icon: 'Code',
            title: 'Smart Contract Development',
            description: 'ERC-20, ERC-721, ERC-1155 standard smart contract development'
          },
          {
            icon: 'Globe',
            title: 'DApp Development',
            description: 'Decentralized application frontend and backend development, Web3 integration'
          },
          {
            icon: 'Layers',
            title: 'NFT Platform Development',
            description: 'NFT minting, trading, and auction platform development'
          },
          {
            icon: 'TrendingUp',
            title: 'DeFi Protocol Development',
            description: 'Decentralized finance protocol and product development'
          },
          {
            icon: 'Coins',
            title: 'Tokenomics Design',
            description: 'Token economic model design and smart contract implementation'
          },
          {
            icon: 'Shield',
            title: 'Security Audit',
            description: 'Smart contract security audit and vulnerability detection'
          }
        ]
      },
      techSpecs: [
        { key: 'methodology', value: 'Agile Development' },
        { key: 'security', value: 'Enterprise-grade Security' },
        { key: 'scalability', value: 'High Scalability' },
        { key: 'support', value: '24/7 Technical Support' },
        { key: 'deployment', value: 'Multi-environment Deployment' },
        { key: 'integration', value: 'Third-party Integration' },
        { key: 'testing', value: 'Comprehensive Testing' },
        { key: 'maintenance', value: 'Continuous Maintenance' }
      ]
    }
  }
  
  const currentContent = content[locale as keyof typeof content] || content.zh
  

  const renderIcon = (iconName: string, className?: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap]
    if (!IconComponent) {
      return <div className={`bg-primary/20 rounded ${className}`} />
    }
    return <IconComponent className={className} />
  }

  return (
    <div className="relative isolate">
      {/* Hero Section */}
      <section className="relative py-20 sm:py-24 lg:py-32 bg-gradient-to-br from-primary/5 via-background to-primary/10 dark:from-primary/10 dark:via-background dark:to-primary/5 overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-10 w-32 h-32 bg-primary/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-primary/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-primary/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '4s' }} />
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-4xl text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge className="mb-6 px-4 py-2 bg-primary/10 text-primary border-primary/20">
                <Code className="w-4 h-4 mr-2" />
                {tCustomDev('badge')}
              </Badge>
            </motion.div>

            <motion.h1
              className="text-4xl font-bold tracking-tight text-foreground sm:text-6xl lg:text-7xl mb-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <span className="bg-gradient-to-r from-primary via-primary/80 to-primary/90 bg-clip-text text-transparent">
                {tCustomDev('heroTitle')}
              </span>
            </motion.h1>

            <motion.p
              className="text-lg sm:text-xl text-muted-foreground mb-8 leading-relaxed max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {tCustomDev('heroSubtitle')}
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-xl font-semibold shadow-lg">
                <Sparkles className="w-5 h-5 mr-2" />
                {tCustomDev('consultNow')}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button size="lg" variant="outline" className="border-primary/20 text-primary hover:bg-primary/5 px-8 py-3 rounded-xl font-semibold">
                <Target className="w-5 h-5 mr-2" />
                {tCustomDev('viewPortfolio')}
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Features Section */}
      <section className="py-20 sm:py-24 bg-background">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-4">
              {tCustomDev('developmentServices')}
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {tCustomDev('coreDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {currentContent.services.map((feature, index) => (
              <motion.div
                key={feature.title}
                className="relative group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
              >
                <div className="relative p-8 bg-card rounded-2xl border border-border shadow-sm hover:shadow-xl transition-all duration-300 h-full">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    {renderIcon(feature.icon, "w-6 h-6 text-white")}
                  </div>
                  <h3 className="text-xl font-semibold text-card-foreground mb-3">{feature.title}</h3>
                  <p className="text-muted-foreground leading-relaxed mb-4">{feature.description}</p>
                  <div className="text-sm text-primary font-medium">{feature.tech}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Blockchain Development Spotlight */}
      <section className="py-20 sm:py-24 bg-gradient-to-br from-purple-50 via-primary/5 to-blue-50 dark:from-purple-950/30 dark:via-primary/10 dark:to-blue-950/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-4">
              区块链开发
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              提供完整的区块链应用开发服务，包括智能合约、DApp、NFT平台等
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: 'Code',
                title: '智能合约开发',
                description: 'ERC-20, ERC-721, ERC-1155等标准智能合约开发'
              },
              {
                icon: 'Globe',
                title: 'DApp应用开发',
                description: '去中心化应用前后端开发，Web3集成'
              },
              {
                icon: 'Layers',
                title: 'NFT平台开发',
                description: 'NFT铸造、交易、拍卖平台开发'
              },
              {
                icon: 'TrendingUp',
                title: 'DeFi协议开发',
                description: '去中心化金融协议和产品开发'
              },
              {
                icon: 'Coins',
                title: '代币经济设计',
                description: '代币经济模型设计和智能合约实现'
              },
              {
                icon: 'Shield',
                title: '安全审计',
                description: '智能合约安全审计和漏洞检测'
              }
            ].map((specialty, index) => (
              <motion.div
                key={specialty.title}
                className="relative group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
              >
                <div className="relative p-8 bg-card rounded-2xl border border-border shadow-sm hover:shadow-xl transition-all duration-300 h-full">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-blue-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    {renderIcon(specialty.icon, "w-6 h-6 text-white")}
                  </div>
                  <h3 className="text-xl font-semibold text-card-foreground mb-3">{specialty.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{specialty.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technical Specifications */}
      <section className="py-20 sm:py-24 bg-gradient-to-br from-muted/30 to-primary/5 dark:from-muted/10 dark:to-primary/5">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-4">
              {tCustomDev('techSpecsTitle')}
            </h2>
            <p className="text-lg text-muted-foreground">
              {tCustomDev('coreDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { key: 'methodology', value: '敏捷开发' },
              { key: 'security', value: '企业级安全' },
              { key: 'scalability', value: '高扩展性' },
              { key: 'support', value: '24/7技术支持' },
              { key: 'deployment', value: '多环境部署' },
              { key: 'integration', value: '第三方集成' },
              { key: 'testing', value: '全面测试' },
              { key: 'maintenance', value: '持续维护' }
            ].map((spec, index) => (
              <motion.div
                key={spec.key}
                className="bg-card p-6 rounded-xl border border-border shadow-sm hover:shadow-md transition-shadow duration-300"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-8 h-8 rounded-lg bg-primary flex items-center justify-center">
                    {renderIcon("CheckCircle", "w-4 h-4 text-white")}
                  </div>
                  <h3 className="font-semibold text-card-foreground">{spec.key}</h3>
                </div>
                <p className="text-muted-foreground">{spec.value}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 sm:py-24 bg-gradient-to-r from-primary via-primary/90 to-primary/80">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-white sm:text-4xl mb-4">
              {tCustomDev('readyToStart')}
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              {tCustomDev('ctaDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-background text-primary hover:bg-background/90 px-8 py-3 rounded-xl font-semibold">
                <HeartHandshake className="w-5 h-5 mr-2" />
                {tCustomDev('consultNow')}
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 px-8 py-3 rounded-xl font-semibold">
                <Award className="w-5 h-5 mr-2" />
                {tCustomDev('viewPortfolio')}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
