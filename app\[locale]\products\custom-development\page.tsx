import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getProductDetails } from "@/lib/products-data"
import { getTranslations } from 'next-intl/server'
import { ProductDetailClient } from "./components/ProductDetailClient"

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com';

interface Props {
  params: { locale: string }
}

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale })
  const isEnglish = locale === 'en'

  const title = isEnglish
    ? 'Custom Development Services - 0dot'
    : '定制化开发服务 - 零点科技'

  const description = isEnglish
    ? 'Professional custom software development services including web applications, mobile apps, and enterprise software. Tailored digital solutions for your business needs.'
    : '专业的定制化软件开发服务，包括Web应用、移动应用和企业软件开发。为您的业务需求量身定制数字化解决方案。'

  const keywords = isEnglish
    ? ['custom development', 'web development', 'mobile app development', 'enterprise software', 'software customization', 'digital solutions', 'full-stack development', 'system integration']
    : ['定制开发', 'Web开发', '移动应用开发', '企业软件', '软件定制', '数字化解决方案', '全栈开发', '系统集成']

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url: `${baseUrl}/${locale}/products/custom-development`,
      siteName: isEnglish ? '0dot' : '零点科技',
      images: [
        {
          url: `${baseUrl}/og-custom-development.jpg`,
          width: 1200,
          height: 630,
          alt: isEnglish ? '0dot Custom Development Services' : '零点科技定制开发服务',
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`${baseUrl}/og-custom-development.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/products/custom-development`,
      languages: {
        'zh-CN': `${baseUrl}/zh/products/custom-development`,
        'en-US': `${baseUrl}/en/products/custom-development`,
      },
    },
  }
}

export default async function CustomDevelopmentPage({ params: { locale } }: Props) {
  const t = await getTranslations({ locale })
  const productDetails = getProductDetails(t)
  const product = productDetails['custom-development']

  if (!product) {
    notFound()
  }

  return <ProductDetailClient product={product} productSlug="custom-development" />
}
