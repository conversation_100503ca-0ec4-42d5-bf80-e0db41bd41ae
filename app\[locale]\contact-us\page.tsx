import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { isValidLocale } from '@/lib/i18n'
import ContactUsClient from './components/ContactUsClient'

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }): Promise<Metadata> {
  if (!isValidLocale(locale)) {
    notFound();
  }

  const isEnglish = locale === 'en';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://0dot.com';

  return {
    title: isEnglish
      ? 'Contact Us - 0dot | Get Professional Technical Support & Consultation'
      : '联系我们 - 0dot | 获取专业技术支持与咨询服务',
    description: isEnglish
      ? 'Contact 0dot for professional technical support, business consultation, and custom solutions. 24/7 customer service, multiple contact methods, and expert teams ready to help.'
      : '联系0dot获取专业技术支持、商务咨询和定制解决方案。7×24小时客服支持，多种联系方式，专业团队随时为您服务。',
    keywords: isEnglish
      ? ['0dot contact', 'technical support', 'customer service', 'business consultation', 'AI solutions support', 'cloud computing help', 'enterprise support', 'professional consultation', '24/7 support', 'contact information']
      : ['0dot联系方式', '技术支持', '客户服务', '商务咨询', 'AI解决方案支持', '云计算帮助', '企业支持', '专业咨询', '24小时支持', '联系信息'],
    openGraph: {
      title: isEnglish
        ? 'Contact Us - 0dot | Get Professional Technical Support & Consultation'
        : '联系我们 - 0dot | 获取专业技术支持与咨询服务',
      description: isEnglish
        ? 'Contact 0dot for professional technical support, business consultation, and custom solutions. 24/7 customer service, multiple contact methods, and expert teams ready to help.'
        : '联系0dot获取专业技术支持、商务咨询和定制解决方案。7×24小时客服支持，多种联系方式，专业团队随时为您服务。',
      url: `${baseUrl}/${locale}/contact-us`,
      siteName: '0dot',
      images: [
        {
          url: `${baseUrl}/og-contact.jpg`,
          width: 1200,
          height: 630,
          alt: isEnglish ? '0dot Contact Us' : '0dot联系我们',
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: isEnglish
        ? 'Contact Us - 0dot | Get Professional Technical Support & Consultation'
        : '联系我们 - 0dot | 获取专业技术支持与咨询服务',
      description: isEnglish
        ? 'Contact 0dot for professional technical support, business consultation, and custom solutions.'
        : '联系0dot获取专业技术支持、商务咨询和定制解决方案。',
      images: [`${baseUrl}/og-contact.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/contact-us`,
      languages: {
        'zh-CN': `${baseUrl}/contact-us`,
        'en-US': `${baseUrl}/en/contact-us`,
      },
    },
  };
}

export default function ContactUs({ params: { locale } }: { params: { locale: string } }) {
  if (!isValidLocale(locale)) {
    notFound();
  }

  return <ContactUsClient />;
}