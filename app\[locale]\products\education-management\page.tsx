import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { getProductDetails } from '@/lib/products-data'
import { ProductDetailClient } from './components/ProductDetailClient'
import { notFound } from 'next/navigation'

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'

interface Props {
  params: { locale: string }
}

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale })
  const productDetails = getProductDetails(t)
  const product = productDetails['education-management']

  if (!product) {
    return {
      title: 'Product Not Found',
      description: 'The requested product could not be found.'
    }
  }

  const isEnglish = locale === 'en'
  const title = isEnglish 
    ? `${product.name} - 0dot` 
    : `${product.name} - 零点科技`
  
  const description = product.description

  return {
    title,
    description,
    keywords: isEnglish 
      ? ['education management', 'learning management system', 'online education', 'course management', 'student tracking']
      : ['教育管理', '学习管理系统', '在线教育', '课程管理', '学生跟踪'],
    openGraph: {
      title,
      description,
      url: `${baseUrl}/${locale}/products/education-management`,
      siteName: isEnglish ? '0dot' : '零点科技',
      images: [
        {
          url: `${baseUrl}/og-education-management.jpg`,
          width: 1200,
          height: 630,
          alt: product.name,
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`${baseUrl}/og-education-management.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/products/education-management`,
      languages: {
        'zh-CN': `${baseUrl}/zh/products/education-management`,
        'en-US': `${baseUrl}/en/products/education-management`,
      },
    },
  }
}

export default async function EducationManagementPage({ params: { locale } }: Props) {
  const t = await getTranslations({ locale })
  const productDetails = getProductDetails(t)
  const product = productDetails['education-management']

  if (!product) {
    notFound()
  }

  return <ProductDetailClient product={product} productSlug="education-management" />
}
